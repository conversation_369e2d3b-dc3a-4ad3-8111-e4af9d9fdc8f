import React, { useState, useRef, useEffect } from "react";
import { Save, Search } from "lucide-react";
import { Userapi } from "../../service/apius/Userapi";
import { useResponse, User } from "../../service/type/AuthUser";
import ComponentCard from "../../components/common/ComponentCard";

import Input from "../../components/form/input/InputField";
import Label from "../../components/form/Label";
import TextArea from "../../components/form/input/TextArea";
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow,
} from "../../components/table";
import CheckBox from "../../components/form/input/CheckBox";

interface BranchLocation {
  id: string;
  name: string;
  address: string;
  longitude: number;
  latitude: number;
  radius: number;
}

declare global {
  interface Window {
    L: any; // Leaflet global
  }
}
const Branchadd = () => {
  const [branches, setBranches] = useState<BranchLocation[]>([]);
  const [userData, setuserData] = useState<User[]>([]);
  const [searchLocation, setSearchLocation] = useState("cambodia");
  const [map, setMap] = useState<any>(null);
  const [markers, setMarkers] = useState<any[]>([]);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    address: "",
    location_name: "",
    longitude: "",
    latitude: "",
    radius: "50",
    user_ids: [] as number[],
  });

  const mapRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const fetchuser = async () => {
      try {
        const response: useResponse = await Userapi.getUser();
        // console.log("User data fetched successfully:", response.data);
        setuserData(response.data);
      } catch (error) {
        console.error("Failed to fetch users:", error);
      }
    };

    fetchuser();
  }, []);
  // Initialize map
  useEffect(() => {
    // Wait for the component to be fully mounted
    const timer = setTimeout(() => {
      if (!mapRef.current) return;

      // Load Leaflet CSS and JS
      const loadLeaflet = async () => {
        // Add CSS
        if (!document.querySelector("link[href*='leaflet']")) {
          const link = document.createElement("link");
          link.rel = "stylesheet";
          link.href =
            "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.css";
          document.head.appendChild(link);
        }

        // Load JS
        if (!window.L) {
          const script = document.createElement("script");
          script.src =
            "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.js";
          document.head.appendChild(script);

          script.onload = () => {
            // Add a small delay to ensure DOM is ready
            setTimeout(() => {
              initializeMap();
            }, 100);
          };
        } else {
          // Add a small delay to ensure DOM is ready
          setTimeout(() => {
            initializeMap();
          }, 100);
        }
      };

      const initializeMap = () => {
        if (!window.L || map || !mapRef.current) return;

        // Check if the container has dimensions
        const container = mapRef.current;
        if (container.offsetWidth === 0 || container.offsetHeight === 0) {
          console.warn("Map container has no dimensions, retrying...");
          setTimeout(initializeMap, 100);
          return;
        }

        try {
          // Initialize map centered on Phnom Penh, Cambodia
          const leafletMap = window.L.map(container, {
            center: [11.5564, 104.9282],
            zoom: 13,
            zoomControl: true,
          });

          // Add tile layer
          window.L.tileLayer(
            "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
            {
              attribution: "© OpenStreetMap contributors",
            }
          ).addTo(leafletMap);

          // Add click handler
          leafletMap.on("click", (e: any) => {
            const { lat, lng } = e.latlng;
            setFormData((prev) => ({
              ...prev,
              longitude: lng.toFixed(10),
              latitude: lat.toFixed(10),
            }));
          });

          setMap(leafletMap);
        } catch (error) {
          console.error("Error initializing map:", error);
          // Retry after a delay
          setTimeout(initializeMap, 500);
        }
      };

      loadLeaflet();
    }, 50); // Small delay to ensure DOM is ready

    return () => {
      clearTimeout(timer);
      if (map) {
        map.remove();
      }
    };
  }, []);

  // Update markers when branches change
  useEffect(() => {
    if (!map || !window.L) return;

    // Clear existing markers
    markers.forEach((marker) => map.removeLayer(marker));
    setMarkers([]);

    // Add new markers
    const newMarkers: any[] = [];

    branches.forEach((branch) => {
      // Create marker
      const marker = window.L.marker([branch.latitude, branch.longitude]).addTo(
        map
      ).bindPopup(`
          <div style="text-align: center;">
            <strong>${branch.name}</strong><br/>
            ${branch.address}<br/>
            <small>Lat: ${branch.latitude.toFixed(
              6
            )}, Lng: ${branch.longitude.toFixed(6)}</small>
          </div>
        `);

      // Create radius circle
      const circle = window.L.circle([branch.latitude, branch.longitude], {
        color: "#3b82f6",
        fillColor: "#3b82f6",
        fillOpacity: 0.1,
        radius: branch.radius * 10, // Convert to meters (approximate)
      }).addTo(map);

      // Add longitude and latitude lines
      const latLine = window.L.polyline(
        [
          [branch.latitude, branch.longitude - 0.01],
          [branch.latitude, branch.longitude + 0.01],
        ],
        {
          color: "#4a5568",
          weight: 2,
          dashArray: "5, 5",
        }
      ).addTo(map);

      const lngLine = window.L.polyline(
        [
          [branch.latitude - 0.01, branch.longitude],
          [branch.latitude + 0.01, branch.longitude],
        ],
        {
          color: "#4a5568",
          weight: 2,
          dashArray: "5, 5",
        }
      ).addTo(map);

      // Add radius label
      const radiusEndPoint = [
        branch.latitude + branch.radius * 10 * 0.00001,
        branch.longitude,
      ];

      const radiusLine = window.L.polyline(
        [[branch.latitude, branch.longitude], radiusEndPoint],
        {
          color: "#3b82f6",
          weight: 2,
        }
      ).addTo(map);

      // Add labels using divIcon
      const radiusLabel = window.L.marker(radiusEndPoint, {
        icon: window.L.divIcon({
          className: "radius-label",
          html: `<div style="background: white; padding: 2px 4px; border-radius: 3px; font-size: 12px; font-weight: bold; color: #374151; border: 1px solid #d1d5db;">radius</div>`,
          iconSize: [50, 20],
          iconAnchor: [25, 10],
        }),
      }).addTo(map);

      const coordLabel = window.L.marker(
        [branch.latitude - 0.008, branch.longitude + 0.008],
        {
          icon: window.L.divIcon({
            className: "coord-label",
            html: `<div style="background: white; padding: 2px 4px; border-radius: 3px; font-size: 12px; font-weight: bold; color: #374151; border: 1px solid #d1d5db;">longitude and latitude</div>`,
            iconSize: [140, 20],
            iconAnchor: [70, 10],
          }),
        }
      ).addTo(map);

      // Add click handler to marker for info popup only
      marker.on("click", () => {
        marker.openPopup();
      });

      newMarkers.push(
        marker,
        circle,
        latLine,
        lngLine,
        radiusLine,
        radiusLabel,
        coordLabel
      );
    });

    setMarkers(newMarkers);
  }, [branches, map]);

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle user assignment checkbox changes
  const handleUserAssignment = (userId: number, isChecked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      user_ids: isChecked
        ? [...prev.user_ids, userId]
        : prev.user_ids.filter((id) => id !== userId),
    }));
  };

  const handleSaveBranch = () => {
    if (
      !formData.name ||
      !formData.address ||
      !formData.longitude ||
      !formData.latitude
    ) {
      alert("Please fill in all required fields");
      return;
    }

    const newBranch: BranchLocation = {
      id: Date.now().toString(),
      name: formData.name,
      address: formData.address,
      longitude: parseFloat(formData.longitude),
      latitude: parseFloat(formData.latitude),
      radius: parseInt(formData.radius),
    };

    setBranches((prev) => [...prev, newBranch]);
    handleClearForm();
    alert("Branch added successfully!");
  };

  const handleClearForm = () => {
    setFormData({
      name: "",
      address: "",
      location_name: "",
      longitude: "",
      latitude: "",
      radius: "50",
      user_ids: [] as number[],
    });
  };

  const handleSearchLocation = async () => {
    if (!searchLocation.trim() || !map) return;

    try {
      // Simple geocoding using Nominatim (OpenStreetMap)
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
          searchLocation
        )}&limit=1`
      );
      const data = await response.json();

      if (data && data.length > 0) {
        const { lat, lon } = data[0];
        map.setView([parseFloat(lat), parseFloat(lon)], 13);
      } else {
        alert("Location not found");
      }
    } catch (error) {
      alert("Error searching for location");
    }
  };

  return (
    <ComponentCard title="Add Branch">
      {/* Search Location */}
      <div className="mb-4 flex gap-2">
        <div className="flex-1">
          <Label>Search Branch Location</Label>
          <div className=" relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={24} />
            </div>
            <input
              type="text"
              value={searchLocation}
              onChange={(e) => setSearchLocation(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearchLocation()}
              className="h-11 w-full rounded-lg border appearance-none pl-10 pr-4 py-2.5 text-sm shadow-theme-xs placeholder:text-gray-400 focus:outline-hidden focus:ring-3 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
              placeholder="Enter location to search..."
            />
          </div>
        </div>
        <div className="flex items-end mb-1">
          <button
            onClick={handleSearchLocation}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Search
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Map Section */}
        <div className="space-y-4">
          <div className="relative">
            <div
              ref={mapRef}
              className="w-full h-96 border-2 border-gray-300 rounded-lg overflow-hidden"
              style={{ minHeight: "400px" }}
            />
            <div className="absolute bottom-2 left-2 bg-white bg-opacity-90 px-2 py-1 rounded text-xs z-[1000]">
              Click on map to place branch
            </div>
          </div>
        </div>

        {/* Form Section */}
        <div className="space-y-4">
          <div>
            <Label>
              Branch Name <span className="text-red-600">*</span>
            </Label>
            <Input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter branch name"
            />
          </div>

          <div>
            <Label>
              Branch Address <span className="text-red-600">*</span>
            </Label>
            <TextArea
              value={formData.address}
              onChange={(e) => handleInputChange("address", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter branch address"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>
                Longitude <span className="text-red-600">*</span>
              </Label>

              <Input
                type="text"
                value={formData.longitude}
                onChange={(e) => handleInputChange("longitude", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0.0000000000"
              />
            </div>
            <div>
              <Label>
                Latitude <span className="text-red-600">*</span>
              </Label>
              <Input
                type="text"
                value={formData.latitude}
                onChange={(e) => handleInputChange("latitude", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0.0000000000"
              />
            </div>
          </div>

          <div>
            <Label>
              Radius (meters) <span className="text-red-600">*</span>
            </Label>
            <Input
              type="number"
              value={formData.radius}
              onChange={(e) => handleInputChange("radius", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="50"
              min="10"
              max="5000"
            />
          </div>
          <Label>
            Assignment Management ({formData.user_ids.length} selected)
            <span className="text-red-600">*</span>
          </Label>
          <Table>
            <TableHeader className="border-b border-gray-100 dark:border-white/[0.05]">
              <TableRow>
                <TableCell className="w-1/4">Select</TableCell>
                <TableCell>User Name</TableCell>
              </TableRow>
            </TableHeader>
            <TableBody>
              {userData.map((user) => (
                <TableRow
                  key={user.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <TableCell className="w-1/4 p-2">
                    <CheckBox
                      
                      id={`user-${user.id}`}
                      checked={formData.user_ids.includes(user.id)}
                      className="h-4 w-4"
                      onChange={(isChecked) =>
                        handleUserAssignment(user.id, isChecked)
                      }
                      label="Add"
                    />
                  </TableCell>
                  <TableCell>{user.name}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <div className="flex gap-2">
            <button
              onClick={handleSaveBranch}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Save className="w-4 h-4" />
              <span className="flex mb-1">Add Branch</span>
            </button>

            <button
              onClick={handleClearForm}
              className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              Clear Form
            </button>
          </div>
        </div>
      </div>
    </ComponentCard>
  );
};

export default Branchadd;
